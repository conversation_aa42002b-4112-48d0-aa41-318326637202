import { DueSoon, Overdue, UpToDate } from '@/components/icons';
import { COLOURS } from '@/constants/colours';
import { HealthRecordStatus, HEALTH_RECORD_STATUS_TEXT, HealthRecordStatusType } from '@/constants/strings';

import styles from './styles';
import { PetDetailsProps } from '@/pet';

const { statusUpToDate, statusOverdue, statusDueSoon } = styles;

export type PetHealthRecordsComponentProps = {
  pet: PetDetailsProps;
}

export type VaccinationRecord = {
  name: string;
  status: HealthRecordStatusType;
}

export type TreatmentRecord = {
  name: string;
  date: string;
  type: string;
}

export type VetVisitRecord = {
  date: string;
  duration: string;
  title: string;
  reason: string;
}

export type ChatHistoryRecord = {
  date: string;
  title: string;
  time: string;
  duration?: string;
}

export const getColor = (status: string) => {
  switch (status) {
    case HealthRecordStatus.UP_TO_DATE:
      return COLOURS.primary;
    case HealthRecordStatus.OVERDUE:
      return COLOURS.redText;
    case HealthRecordStatus.DUE_SOON:
      return COLOURS.goldText;
    default:
      return COLOURS.primary;
  }
};

export const StatusIcon = (status: string) => {
  switch (status) {
    case HealthRecordStatus.UP_TO_DATE:
      return UpToDate;
    case HealthRecordStatus.OVERDUE:
      return Overdue;
    case HealthRecordStatus.DUE_SOON:
      return DueSoon;
    default:
      return UpToDate;
  }
};

export const getStatusStyle = (status: string) => {
  switch (status) {
    case HealthRecordStatus.UP_TO_DATE:
      return statusUpToDate;
    case HealthRecordStatus.OVERDUE:
      return statusOverdue;
    case HealthRecordStatus.DUE_SOON:
      return statusDueSoon;
    default:
      return statusUpToDate;
  }
};

export const getStatusText = (status: string) => {
  return HEALTH_RECORD_STATUS_TEXT[status as HealthRecordStatusType] || HEALTH_RECORD_STATUS_TEXT[HealthRecordStatus.UP_TO_DATE];
};

// Mock data - in real app this would come from props or API
export const vaccinations: VaccinationRecord[] = [
  { name: 'Rabies', status: HealthRecordStatus.UP_TO_DATE },
  { name: 'Parvovirus', status: HealthRecordStatus.OVERDUE },
  { name: 'Kennel Cough', status: HealthRecordStatus.DUE_SOON },
];

export const vetVisits: VetVisitRecord[] = [
  {
    date: '17 Jun 2024',
    duration: '30 min',
    title: 'Video Consultation with Dr. Sarah',
    reason: `Arlo's chocolate toxicity`,
  },
];

export const chatHistory: ChatHistoryRecord[] = [
  {
    date: 'June 2024',
    title: 'Golden Retriever Grooming',
    time: '30 min ago',
  },
  {
    date: '',
    title: `Arlo's chocolate toxicity`,
    duration: '20 Min',
    time: '2 hours ago',
  },
  {
    date: '',
    title: 'Puppy Biting Behaviour',
    time: '1 week ago',
  },
];
