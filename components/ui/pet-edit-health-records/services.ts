import { EditPetFormData } from '@/app/(tabs)/my-pets/edit-pet/services';
import { DueSoon, Overdue, UpToDate } from '@/components/icons';
import { COLOURS } from '@/constants/colours';
import { HealthRecordStatus, HEALTH_RECORD_STATUS_TEXT, HealthRecordStatusType } from '@/constants/strings';

import styles from './styles';

export type PetEditHealthRecordsProps = {
  values: EditPetFormData;
  errors: any;
  touched: any;
  handleChange: any;
  handleBlur: any;
  setFieldValue: (field: string, value: any) => void;
  submitCount: number;
  vaccinations: VaccinationRecord[];
  setVaccinations: (vaccinations: VaccinationRecord[]) => void;
  treatments: TreatmentRecord[];
  setTreatments: (treatments: TreatmentRecord[]) => void;
}

export type VaccinationRecord = {
  id: number;
  name: string;
  date: string;
  status: 'up-to-date' | 'overdue' | 'due-soon';
}

export type TreatmentRecord = {
  id: number;
  name: string;
  date: string | null;
  status: HealthRecordStatusType;
}

// Use the same functions as pet-health-records component
export const getColor = (status: string) => {
  switch (status) {
    case HealthRecordStatus.UP_TO_DATE:
      return COLOURS.primary;
    case HealthRecordStatus.OVERDUE:
      return COLOURS.redText;
    case HealthRecordStatus.DUE_SOON:
      return COLOURS.goldText;
    default:
      return COLOURS.primary;
  }
};

export const StatusIcon = (status: string) => {
  switch (status) {
    case HealthRecordStatus.UP_TO_DATE:
      return UpToDate;
    case HealthRecordStatus.OVERDUE:
      return Overdue;
    case HealthRecordStatus.DUE_SOON:
      return DueSoon;
    default:
      return UpToDate;
  }
};

export const getStatusText = (status: string) => {
  return HEALTH_RECORD_STATUS_TEXT[status as HealthRecordStatusType] || HEALTH_RECORD_STATUS_TEXT[HealthRecordStatus.UP_TO_DATE];
};

export const getStatusStyle = (status: string) => {
  const { statusUpToDate, statusOverdue, statusDueSoon } = styles;
  switch (status) {
    case HealthRecordStatus.UP_TO_DATE:
      return statusUpToDate;
    case HealthRecordStatus.OVERDUE:
      return statusOverdue;
    case HealthRecordStatus.DUE_SOON:
      return statusDueSoon;
    default:
      return statusUpToDate;
  }
};

// Mock vaccination types for dropdown
export const vaccinationTypes = [
  { label: 'Rabies', value: 'Rabies' },
  { label: 'Parvovirus', value: 'Parvovirus' },
  { label: 'Kennel Cough', value: 'Kennel Cough' },
  { label: 'Distemper', value: 'Distemper' },
  { label: 'Hepatitis', value: 'Hepatitis' },
];

// Mock treatment types for dropdown
export const treatmentTypes = [
  { label: 'Select Treatment', value: '' },
  { label: 'Worming', value: 'Worming' },
  { label: 'Flea Treatment', value: 'Flea Treatment' },
  { label: 'Tick Prevention', value: 'Tick Prevention' },
  { label: 'Heartworm Prevention', value: 'Heartworm Prevention' },
];
